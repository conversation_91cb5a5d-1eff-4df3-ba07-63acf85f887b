import type { StackProps } from '@mui/material/Stack';

import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';


// ----------------------------------------------------------------------

export function NavUpgrade({ sx, ...other }: StackProps) {
  const navigate = useNavigate();
  const { t } = useTranslation();
  
  return (
    <>      
      
      {/* Upgrade Button */}
      <Box
        display="flex"
        alignItems="center"
        flexDirection="column"
        sx={{ mb: 4, textAlign: 'center', ...sx }}
        {...other}
      >
        <Button
          onClick={() => {
            navigate("/upgrade-license")
          }}
          variant="contained"
          color="primary"
          sx={(theme) => ({
            px: 4,
            py: 1.5,
            borderRadius: '20px',
            boxShadow: '0px 4px 15px rgba(0,0,0,0.2)',
            fontWeight: 'bold',
            textTransform: 'none',
            background: `linear-gradient(to right, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
            '&:hover': {
              background: `linear-gradient(to right, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
            },
          })}
        >
          {t('upgrade.upgradeToPro', 'Upgrade to Pro 🚀')}
        </Button>
      </Box>
    </>
  );
}
