import { z } from 'zod';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, FormProvider } from 'react-hook-form';

import { DashboardContent } from 'src/layouts/dashboard';
import { useConnectWixMutation } from 'src/services/apis/integrations/wixApi';
import { useConnectShopifyMutation } from 'src/services/apis/integrations/shopifyApi';
import { useConnectWordPressMutation } from 'src/services/apis/integrations/wordpressApi';

import { FormStepper } from 'src/components/stepper/FormStepper';

import SelectPlatform from './SelectPlatform';
import WebsiteDetails from './WebsiteDetails';
import { SuccessAnimation } from './SuccessAnimation';

// Test mode for bypassing API errors
const TEST_MODE = true;

export const platforms = [
  {
    id: 'wordpress',
    name: 'WordPress',
    icon: '/assets/icons/platforms/wordpress.svg',
    description: 'Connect your WordPress site',
  },
  {
    id: 'shopify',
    name: 'Shopify',
    icon: '/assets/icons/platforms/shopify.svg',
    description: 'Connect your Shopify store',
  },
  {
    id: 'wix',
    name: 'Wix',
    icon: '/assets/icons/platforms/wix.svg',
    description: 'Connect your Wix store',
  },
];

// Create a schema for form validation
const storeFormSchema = z.object({
  platform: z.string().min(1, "Please select a platform"),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: "You must accept the terms and conditions",
  }),
  
  // WordPress specific fields with validation
  store_url: z.string().min(1, "WordPress URL is required")
    .url("Please enter a valid URL"),
  store_username: z.string().min(1, "WordPress username is required").email(),
  store_password: z.string().min(1, "WordPress password is required"),
  
  // Shopify specific fields
  shopifyStore: z.string().optional(),
  apiKey: z.string().optional(),
  apiSecret: z.string().optional(),
  
  // Wix specific fields
  adminUrl: z.string().optional(),
  consumerKey: z.string().optional(),
  consumerSecret: z.string().optional(),
});

// Create a type from the schema
export type StoreFormData = z.infer<typeof storeFormSchema>;

export default function AddStoreFlow() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(0);
  const [integrationSuccess, setIntegrationSuccess] = useState(false);
  
  // API mutation hooks
  const [connectWordPress , {isLoading : isWordPressLoading}] = useConnectWordPressMutation();
  const [connectShopify ,  {isLoading : isShopifyLoading}] = useConnectShopifyMutation();
  const [connectWix, {isLoading : isWixLoading}] = useConnectWixMutation();

  const isLoading = isWordPressLoading || isShopifyLoading || isWixLoading;

  const methods = useForm<StoreFormData>({
    resolver: zodResolver(storeFormSchema),
    defaultValues: {
      platform: '',
      acceptTerms: false,
    },
    mode: 'onChange',
  });

  const { watch, setValue, trigger } = methods;
  const selectedPlatform = watch('platform');

  const steps = [
    t('store.selectPlatform'),
    t('store.title')
  ];

  const handleNext = async () => {
    // Validate current step before proceeding
    if (activeStep === 0) {
      const isValid = await trigger('platform');
      if (!isValid) return;
    }
    
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    if (activeStep === 0) {
      navigate('/stores');
    } else {
      setActiveStep((prevStep) => prevStep - 1);
    }
  };

  // Centralized submit handler for all platforms
  const handleSubmit = async (data: StoreFormData) => {
    try {
      switch (data.platform) {
        case 'wordpress':
          await handleWordPressConnection(data);
          break;
        case 'shopify':
          await handleShopifyConnection(data);
          break;
        case 'wix':
          await handleWixConnection(data);
          break;
        default:
          throw new Error('Unsupported platform');
      }
      setTimeout(() => {
        setIntegrationSuccess(true);
        navigate('/stores');
      }, 3000);
    } catch (errore) {
      if (TEST_MODE) {
        setTimeout(() => {
          navigate('/stores');
        }, 3000);
      } else {
        setTimeout(() => {
          setIntegrationSuccess(true);
          navigate('/stores');
        }, 3000);
      }
    }
  };

  // Platform-specific connection handlers
  const handleWordPressConnection = async (data: StoreFormData) => {    
    await connectWordPress({
      store_url: data.store_url || '',
      store_username: data.store_username || '',
      store_password: data.store_password || '',
    }).unwrap()
      .then(() => {
        toast.success(t('store.success'));
      })
      .catch(() => {
        if (TEST_MODE) {
          setIntegrationSuccess(true);
          toast.success(t('store.success'));
        }
      })
  };

  const handleShopifyConnection = async (data: StoreFormData) => {};

  const handleWixConnection = async (data: StoreFormData) => {};

  // Handle platform selection
  const handlePlatformSelect = (platform: string) => {
    setValue('platform', platform);
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <SelectPlatform 
            selectedPlatform={selectedPlatform}
            onSelectPlatform={handlePlatformSelect}
            onNext={handleNext}
            onBack={handleBack}
            platforms={platforms}
          />
        );
      case 1:
        return (
          <WebsiteDetails
            onSubmit={methods.handleSubmit(handleSubmit)}
            onBack={handleBack}
            isLoading={isLoading}
          />
        );
      default:
        return null;
    }
  };

  return (
    <DashboardContent>
      <FormStepper
        steps={steps}
        activeStep={activeStep}
      />
      
      <FormProvider {...methods}>
        {renderStepContent()}
      </FormProvider>
      
      <SuccessAnimation integrationSuccess={integrationSuccess} />
    </DashboardContent>
  );
}