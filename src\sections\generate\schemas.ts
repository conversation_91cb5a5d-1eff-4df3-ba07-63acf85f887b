import { z } from 'zod';

// Step 1: Content Setup Schema
export const step1Schema = z.object({
  contentDescription: z.string().min(1, 'Content description is required'),
  primaryKeyword: z.string().min(1, 'Primary keyword is required'),
  secondaryKeywords: z.array(z.string()).min(1, 'At least one secondary keyword is required'),
  language: z.string().min(1, 'Language is required'),
  targetCountry: z.string().min(1, 'Target country is required'),
  title: z.string().optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  urlSlug: z.string().optional(),
});

// Step 2: Article Settings Schema
export const step2Schema = z.object({
  articleType: z.string().min(1, 'Article type is required'),
  articleSize: z.string().min(1, 'Article size is required'),
  toneOfVoice: z.string().min(1, 'Tone of voice is required'),
  pointOfView: z.string().min(1, 'Point of view is required'),
  aiContentCleaning: z.string().min(1, 'AI content cleaning is required'),
  // Simplified media settings
  includeImages: z.boolean().default(true),
  includeVideos: z.boolean().default(false),
  // Linking settings
  internalLinking: z.string().min(1, 'Internal linking is required'),
  externalLinking: z.string().min(1, 'External linking is required'),
  // Legacy fields for backward compatibility (optional)
  imageSettingsQuality: z.string().optional(),
  imageSettingsPlacement: z.string().optional(),
  imageSettingsStyle: z.string().optional(),
  imageSettingsCount: z.number().optional(),
  numberOfVideos: z.number().default(1),
});

// Section content type schemas
export const sectionLinkSchema = z.object({
  text: z.string(),
  url: z.string(),
});

export const sectionImageSchema = z.object({
  url: z.string(),
  alt: z.string(),
  caption: z.string().optional(),
});

export const sectionFaqItemSchema = z.object({
  question: z.string(),
  answer: z.string(),
});

export const sectionTableDataSchema = z.object({
  headers: z.array(z.string()),
  rows: z.array(z.array(z.string())),
});

// Enhanced section schema
export const sectionSchema = z.object({
  id: z.string(),
  title: z.string().min(1, 'Section title is required'),
  content: z.string().min(1, 'Section content is required'),
  status: z.string(),
  // Additional properties from step four
  type: z.enum(['introduction', 'regular', 'conclusion', 'faq']).optional(),
  contentType: z.enum(['paragraph', 'bullet-list', 'table', 'faq', 'image-gallery']).optional(),
  bulletPoints: z.array(z.string()).optional(),
  internalLinks: z.array(sectionLinkSchema).optional(),
  externalLinks: z.array(sectionLinkSchema).optional(),
  tableData: sectionTableDataSchema.optional(),
  faqItems: z.array(sectionFaqItemSchema).optional(),
  images: z.array(sectionImageSchema).optional(),
  // Keep subsections for backward compatibility
  subsections: z.array(z.object({
    id: z.string(),
    title: z.string().min(1, 'Subsection title is required'),
    content: z.string().min(1, 'Subsection content is required'),
    status: z.string(),
  })).optional(),
});

// Step 3: Content Structure Schema
export const step3Schema = z.object({
  sections: z.array(sectionSchema).min(1, 'At least one section is required'),
});


// Combined schema for the entire form
export const generateArticleSchema = z.object({
  step1: step1Schema,
  step2: step2Schema,
  step3: step3Schema,
});

// Export types for section content
export type SectionLink = z.infer<typeof sectionLinkSchema>;
export type SectionImage = z.infer<typeof sectionImageSchema>;
export type SectionFaqItem = z.infer<typeof sectionFaqItemSchema>;
export type SectionTableData = z.infer<typeof sectionTableDataSchema>;
export type ArticleSection = z.infer<typeof sectionSchema>;

// Export form data types
export type Step1FormData = z.infer<typeof step1Schema>;
export type Step2FormData = z.infer<typeof step2Schema>;
export type Step3FormData = z.infer<typeof step3Schema>;
export type GenerateArticleFormData = z.infer<typeof generateArticleSchema>;