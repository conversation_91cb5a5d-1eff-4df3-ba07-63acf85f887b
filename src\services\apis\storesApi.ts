import type { Store } from 'src/types/store';

import { api } from '.';

export const STORES_BASE_URL = '/stores';

interface StoreState {
  stores: Store[];
  count: number;
}

export const storesApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getStores: builder.query<StoreState, void>({
      query: () => ({
        url: `${STORES_BASE_URL}`,
        method: 'GET',
      }),
    }),
    
    deleteStore: builder.mutation<void, number>({
      query: (store_id) => ({
        url: `${STORES_BASE_URL}/${store_id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Stores'],
    }),

    // New disconnectStore mutation endpoint
    disconnectStore: builder.mutation<void, number>({
      query: (store_id) => ({
        url: `${STORES_BASE_URL}/disconnect/${store_id}`,
        method: 'POST',
      }),
      invalidatesTags: ['Stores'],
    }),

    // New reconnectStore mutation endpoint
    reconnectStore: builder.mutation<void, number>({
      query: (store_id) => ({
        url: `${STORES_BASE_URL}/reconnect/${store_id}`,
        method: 'POST',
      }),
      invalidatesTags: ['Stores'],
    }),
  }),
});

export const { 
  useGetStoresQuery, 
  useLazyGetStoresQuery,
  useDeleteStoreMutation,
  useDisconnectStoreMutation,
  useReconnectStoreMutation
} = storesApi;